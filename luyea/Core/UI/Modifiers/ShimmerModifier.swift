import SwiftUI

/// Shimmer 动画效果的视图修饰器
struct ShimmerModifier: ViewModifier {
    @State private var phase: CGFloat = -1.5
    let duration: Double
    let delay: Double

    /// 初始化
    /// - Parameters:
    ///   - duration: 动画周期，默认1.5秒
    ///   - delay: 延迟执行，默认0.2秒
    init(duration: Double = 1.5, delay: Double = 0.2) {
        self.duration = duration
        self.delay = delay
    }

    func body(content: Content) -> some View {
        content
            .overlay(
                ShimmerGradient(phase: phase)
                    .animation(
                        .linear(duration: duration)
                        .delay(delay)
                        .repeatForever(autoreverses: false),
                        value: phase
                    )
                    .onAppear {
                        phase = 1.5
                    }
            )
    }

    /// Shimmer 动画的渐变层
    private struct ShimmerGradient: View {
        let phase: CGFloat

        var body: some View {
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.white.opacity(0.0),
                    Color.white.opacity(0.4),
                    Color.white.opacity(0.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
            .rotationEffect(.degrees(15))
            .offset(x: phase * 200) // 移动渐变
        }
    }
}

extension View {
    /// 为视图添加 Shimmer 加载效果
    /// - Parameters:
    ///   - isActive: 是否激活 Shimmer 效果
    ///   - duration: 动画周期
    ///   - delay: 延迟执行
    /// - Returns: 应用了 Shimmer 效果的视图
    @ViewBuilder
    func shimmer(
        isActive: Bool = true,
        duration: Double = 1.5,
        delay: Double = 0.2
    ) -> some View {
        if isActive {
            modifier(ShimmerModifier(duration: duration, delay: delay))
        } else {
            self
        }
    }
}