import Foundation

// MARK: - Mock分页策略配置文件
//
// 📋 这个文件只包含策略配置，不包含任何业务逻辑
// 🔧 日常维护时，通常只需要修改这个文件中的配置
// 📝 配置格式说明：
//   - .filter(key: "参数名", handler: .筛选类型("字段路径"))
//   - .sort(key: "参数名", handler: .排序类型("字段路径"))
//
// 💡 支持的筛选类型：
//   - .exact("字段路径")           // 精确匹配
//   - .contains("字段路径")        // 包含匹配
//   - .keyword(["字段1", "字段2"]) // 关键词搜索
//   - .status("字段路径")          // 状态筛选
//   - .custom({ item, value in ... }) // 自定义筛选
//
// 💡 支持的排序类型：
//   - .byNumber("字段路径")        // 按数值排序
//   - .byDate("字段路径")          // 按日期排序
//   - .byString("字段路径")        // 按字符串排序
//   - .byCount("字段路径")         // 按数组长度排序
//   - .custom({ item1, item2, ascending in ... }) // 自定义排序

/// Mock分页策略配置列表
public let mockPaginationConfigs: [MockPaginationConfigProtocol] = [
    
    // MARK: - 发现页列表配置
    MockPaginationConfig(
        name: "发现页列表",
        apiPath: APIPaths.discoverList,
        modelType: DiscoverItem.self,
        queryParams: [
            // 筛选参数
            .filter(key: "topicIds", handler: .custom({ item, value in
                guard let discoverItem = item as? DiscoverItem,
                      let topicId = discoverItem.topic?.id else { return false }
                let topicIdArray = value.split(separator: ",").map { String($0).trimmingCharacters(in: .whitespaces) }
                return topicIdArray.contains(topicId)
            })),
            .filter(key: "destinationId", handler: .contains("location")),
            .filter(key: "showLikedOnly", handler: .custom({ _, value in
                return value != "true" // 暂时返回所有数据
            })),
            
            // 排序参数
            .sort(key: "likes", handler: .byNumber("likes")),
            .sort(key: "title", handler: .byString("title")),
            .sort(key: "comments", handler: .byCount("comments"))
        ]
    ),
    
    // MARK: - 我的作品列表配置
    MockPaginationConfig(
        name: "我的作品列表",
        apiPath: APIPaths.myWorksList,
        modelType: MyWorksModels.WorkItem.self,
        queryParams: [
            // 筛选参数
            .filter(key: "topic", handler: .custom({ item, value in
                guard let workItem = item as? MyWorksModels.WorkItem else { return false }
                guard value != "all" else { return true }
                return workItem.topic?.id == value
            })),
            .filter(key: "keyword", handler: .keyword(["title", "description", "location"])),
            .filter(key: "status", handler: .custom({ item, value in
                guard let workItem = item as? MyWorksModels.WorkItem else { return false }
                guard let workStatus = MyWorksModels.WorkStatus(rawValue: value) else { return true }
                return workItem.status == workStatus
            })),
            
            // 排序参数
            .sort(key: "create_time", handler: .byDate("createdAt")),
            .sort(key: "view_count", handler: .byNumber("viewCount")),
            .sort(key: "like_count", handler: .byNumber("likeCount")),
            
            // Fork数排序：支持fork的优先，然后按数值排序
            .sort(key: "fork_count", handler: .custom({ item1, item2, ascending in
                guard let work1 = item1 as? MyWorksModels.WorkItem,
                      let work2 = item2 as? MyWorksModels.WorkItem else { return false }
                
                // 优先级1：allowFork字段 - 支持fork的排在前面
                if work1.allowFork && !work2.allowFork {
                    return true  // work1支持fork，排在前面
                } else if !work1.allowFork && work2.allowFork {
                    return false // work2支持fork，work1排在后面
                }
                
                // 优先级2：在同一组内按fork数排序
                let count1 = work1.forkCount
                let count2 = work2.forkCount
                return ascending ? count1 < count2 : count1 > count2
            }))
        ]
    ),
    
    // MARK: - 行程列表配置
    MockPaginationConfig(
        name: "行程列表",
        apiPath: APIPaths.itineraryPrefix,
        modelType: ItineraryModel.self,
        queryParams: [
            .filter(key: "status", handler: .status("status"))
        ]
    ),

    // MARK: - 评论回复配置（通配符模式，支持动态参数）
    MockPaginationConfig(
        name: "评论回复",
        apiPath: "/api/v1/discover/{contentId}/comments/{commentId}/replies",
        modelType: DiscoverDetailModels.CommentReply.self,
        queryParams: [
            // 排序参数
            .sort(key: "created_at", handler: .byDate("createdAt")),
            .sort(key: "likes", handler: .byNumber("likeCount"))
        ]
    ),

    // MARK: - 发现详情评论配置（通配符模式，支持动态内容ID）
    MockPaginationConfig(
        name: "发现详情评论",
        apiPath: "/api/v1/discover/{contentId}/comments",
        modelType: DiscoverDetailModels.Comment.self,
        queryParams: [
            // 排序参数
            .sort(key: "created_at", handler: .byDate("createdAt")),
            .sort(key: "likes", handler: .byNumber("likeCount"))
        ]
    )

    // MARK: - 新增配置示例（注释掉，需要时取消注释）
    /*
    MockPaginationConfig(
        name: "示例配置",
        apiPath: "/api/example",
        modelType: ExampleModel.self,
        queryParams: [
            // 筛选示例
            .filter(key: "category", handler: .exact("category")),
            .filter(key: "search", handler: .keyword(["title", "content"])),
            .filter(key: "active", handler: .boolean("isActive")),
            
            // 排序示例
            .sort(key: "created_at", handler: .byDate("createdAt")),
            .sort(key: "priority", handler: .byNumber("priority"))
        ]
    )
    */
]
