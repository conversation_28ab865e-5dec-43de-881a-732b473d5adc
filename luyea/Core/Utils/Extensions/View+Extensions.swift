import SwiftUI

// MARK: - View Extensions

extension View {
    // MARK: - Conditional Modifiers
    
    /// 条件性应用修饰符
    /// 
    /// - Parameters:
    ///   - condition: 条件
    ///   - transform: 当条件为true时应用的修饰符
    /// - Returns: 修饰后的视图
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    /// 条件性应用修饰符（带else分支）
    /// 
    /// - Parameters:
    ///   - condition: 条件
    ///   - ifTransform: 当条件为true时应用的修饰符
    ///   - elseTransform: 当条件为false时应用的修饰符
    /// - Returns: 修饰后的视图
    @ViewBuilder
    func `if`<TrueContent: View, FalseContent: View>(
        _ condition: Bool,
        if ifTransform: (Self) -> TrueContent,
        else elseTransform: (Self) -> FalseContent
    ) -> some View {
        if condition {
            ifTransform(self)
        } else {
            elseTransform(self)
        }
    }
    
    /// 可选值条件修饰符
    /// 
    /// - Parameters:
    ///   - value: 可选值
    ///   - transform: 当值不为nil时应用的修饰符
    /// - Returns: 修饰后的视图
    @ViewBuilder
    func ifLet<Value, Content: View>(
        _ value: Value?,
        transform: (Self, Value) -> Content
    ) -> some View {
        if let value = value {
            transform(self, value)
        } else {
            self
        }
    }
    
    // MARK: - Corner Radius

    /// 指定角的统一圆角
    ///
    /// - Parameters:
    ///   - radius: 圆角半径
    ///   - corners: 指定的角
    /// - Returns: 应用圆角的视图
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(UnifiedRoundedCorner(radius: radius, corners: corners))
    }

    /// 四角独立圆角
    ///
    /// - Parameters:
    ///   - topLeft: 左上角圆角半径
    ///   - topRight: 右上角圆角半径
    ///   - bottomLeft: 左下角圆角半径
    ///   - bottomRight: 右下角圆角半径
    /// - Returns: 应用圆角的视图
    func cornerRadius(
        topLeft: CGFloat = 0,
        topRight: CGFloat = 0,
        bottomLeft: CGFloat = 0,
        bottomRight: CGFloat = 0
    ) -> some View {
        clipShape(UnifiedRoundedCorners(
            topLeft: topLeft,
            topRight: topRight,
            bottomLeft: bottomLeft,
            bottomRight: bottomRight
        ))
    }

    /// 便捷方法：顶部圆角
    ///
    /// - Parameter radius: 圆角半径
    /// - Returns: 应用顶部圆角的视图
    func topCornerRadius(_ radius: CGFloat) -> some View {
        cornerRadius(radius, corners: [.topLeft, .topRight])
    }

    /// 便捷方法：底部圆角
    ///
    /// - Parameter radius: 圆角半径
    /// - Returns: 应用底部圆角的视图
    func bottomCornerRadius(_ radius: CGFloat) -> some View {
        cornerRadius(radius, corners: [.bottomLeft, .bottomRight])
    }

    /// 便捷方法：左侧圆角
    ///
    /// - Parameter radius: 圆角半径
    /// - Returns: 应用左侧圆角的视图
    func leftCornerRadius(_ radius: CGFloat) -> some View {
        cornerRadius(radius, corners: [.topLeft, .bottomLeft])
    }

    /// 便捷方法：右侧圆角
    ///
    /// - Parameter radius: 圆角半径
    /// - Returns: 应用右侧圆角的视图
    func rightCornerRadius(_ radius: CGFloat) -> some View {
        cornerRadius(radius, corners: [.topRight, .bottomRight])
    }
    
    // MARK: - Loading State
    
    /// 加载状态覆盖层
    /// 
    /// - Parameters:
    ///   - isLoading: 是否正在加载
    ///   - loadingText: 加载文本
    /// - Returns: 带加载状态的视图
    func loadingOverlay(
        isLoading: Bool,
        loadingText: String = "加载中..."
    ) -> some View {
        overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()
                        
                        VStack(spacing: 12) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(.white)
                            
                            Text(loadingText)
                                .font(.body)
                                .foregroundColor(.white)
                        }
                        .padding(20)
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(12)
                    }
                }
            }
        )
    }
    
    // MARK: - Error Handling
    
    /// 错误提示
    /// 
    /// - Parameters:
    ///   - error: 错误对象
    ///   - isPresented: 是否显示
    ///   - onRetry: 重试回调
    /// - Returns: 带错误提示的视图
    func errorAlert(
        error: Binding<Error?>,
        isPresented: Binding<Bool>,
        onRetry: (() -> Void)? = nil
    ) -> some View {
        alert("错误", isPresented: isPresented) {
            if let onRetry = onRetry {
                Button("重试", action: onRetry)
            }
            Button("确定", role: .cancel) {
                error.wrappedValue = nil
            }
        } message: {
            if let error = error.wrappedValue {
                Text(error.localizedDescription)
            }
        }
    }
    
    // MARK: - Keyboard Handling

    /// 点击隐藏键盘
    ///
    /// - Returns: 支持点击隐藏键盘的视图
    func hideKeyboardOnTap() -> some View {
        onTapGesture {
            UIApplication.shared.sendAction(
                #selector(UIResponder.resignFirstResponder),
                to: nil,
                from: nil,
                for: nil
            )
        }
    }
    
    // MARK: - Navigation
    
    /// 导航栏样式
    /// 
    /// - Parameters:
    ///   - backgroundColor: 背景色
    ///   - titleColor: 标题颜色
    /// - Returns: 设置导航栏样式的视图
    func navigationBarStyle(
        backgroundColor: UIColor = .systemBackground,
        titleColor: UIColor = .label
    ) -> some View {
        onAppear {
            let appearance = UINavigationBarAppearance()
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = backgroundColor
            appearance.titleTextAttributes = [.foregroundColor: titleColor]
            appearance.largeTitleTextAttributes = [.foregroundColor: titleColor]
            
            UINavigationBar.appearance().standardAppearance = appearance
            UINavigationBar.appearance().compactAppearance = appearance
            UINavigationBar.appearance().scrollEdgeAppearance = appearance
        }
    }
    
    // MARK: - Animations
    
    /// 弹簧动画
    /// 
    /// - Parameters:
    ///   - response: 响应时间
    ///   - dampingFraction: 阻尼系数
    ///   - blendDuration: 混合持续时间
    /// - Returns: 带弹簧动画的视图
    func springAnimation(
        response: Double = 0.5,
        dampingFraction: Double = 0.8,
        blendDuration: Double = 0
    ) -> some View {
        animation(.spring(response: response, dampingFraction: dampingFraction, blendDuration: blendDuration), value: UUID())
    }
    
    // MARK: - Accessibility
    
    /// 设置无障碍标识符
    ///
    /// - Parameter identifier: 标识符
    /// - Returns: 设置无障碍标识符的视图
    func accessibilityIdentifier(_ identifier: String) -> some View {
        accessibility(identifier: identifier)
    }
}

// MARK: - Internal Shape Implementations

/// 统一的指定角圆角形状
public struct UnifiedRoundedCorner: Shape, Animatable {
    var radius: CGFloat
    var corners: UIRectCorner

    public var animatableData: CGFloat {
        get { radius }
        set { radius = newValue }
    }

    public func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: max(0, radius), height: max(0, radius))
        )
        return Path(path.cgPath)
    }
}

/// 内部使用的四角独立圆角形状
private struct UnifiedRoundedCorners: Shape {
    var topLeft: CGFloat
    var topRight: CGFloat
    var bottomLeft: CGFloat
    var bottomRight: CGFloat

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let width = rect.size.width
        let height = rect.size.height

        let tl = max(0, topLeft)
        let tr = max(0, topRight)
        let bl = max(0, bottomLeft)
        let br = max(0, bottomRight)

        // 左上角
        path.move(to: CGPoint(x: 0, y: tl))
        path.addQuadCurve(to: CGPoint(x: tl, y: 0), control: CGPoint(x: 0, y: 0))

        // 右上角
        path.addLine(to: CGPoint(x: width - tr, y: 0))
        path.addQuadCurve(to: CGPoint(x: width, y: tr), control: CGPoint(x: width, y: 0))

        // 右下角
        path.addLine(to: CGPoint(x: width, y: height - br))
        path.addQuadCurve(to: CGPoint(x: width - br, y: height), control: CGPoint(x: width, y: height))

        // 左下角
        path.addLine(to: CGPoint(x: bl, y: height))
        path.addQuadCurve(to: CGPoint(x: 0, y: height - bl), control: CGPoint(x: 0, y: height))

        path.closeSubpath()
        return path
    }
}

// MARK: - Preview

#if DEBUG
#Preview("统一圆角 API") {
    VStack(spacing: 16) {
        // 便捷方法示例
        HStack(spacing: 12) {
            Rectangle()
                .fill(Color.blue)
                .frame(width: 80, height: 50)
                .topCornerRadius(12)
                .overlay(Text("顶部").foregroundStyle(.white).font(.caption))

            Rectangle()
                .fill(Color.green)
                .frame(width: 80, height: 50)
                .bottomCornerRadius(12)
                .overlay(Text("底部").foregroundStyle(.white).font(.caption))
        }

        HStack(spacing: 12) {
            Rectangle()
                .fill(Color.orange)
                .frame(width: 80, height: 50)
                .leftCornerRadius(12)
                .overlay(Text("左侧").foregroundStyle(.white).font(.caption))

            Rectangle()
                .fill(Color.purple)
                .frame(width: 80, height: 50)
                .rightCornerRadius(12)
                .overlay(Text("右侧").foregroundStyle(.white).font(.caption))
        }

        // 四角独立示例
        Rectangle()
            .fill(Color.red)
            .frame(width: 120, height: 60)
            .cornerRadius(topLeft: 4, topRight: 8, bottomLeft: 12, bottomRight: 16)
            .overlay(Text("四角独立").foregroundStyle(.white).font(.caption))
    }
    .padding()
}
#endif

// MARK: - Preview Helpers

#if DEBUG
extension View {
    /// 预览设备（便捷方法）
    ///
    /// - Parameter deviceName: 设备名称字符串
    /// - Returns: 指定设备的预览
    func previewDevice(_ deviceName: String) -> some View {
        previewDevice(PreviewDevice(rawValue: deviceName))
    }

    /// 预览尺寸固定
    ///
    /// - Parameters:
    ///   - width: 宽度
    ///   - height: 高度
    /// - Returns: 固定尺寸的预览
    func previewFixedSize(width: CGFloat, height: CGFloat) -> some View {
        previewLayout(.fixed(width: width, height: height))
    }
}
#endif

// MARK: - Debug Helpers

#if DEBUG
extension View {
    /// 调试边框
    /// 
    /// - Parameter color: 边框颜色
    /// - Returns: 带调试边框的视图
    func debugBorder(_ color: Color = .red) -> some View {
        border(color, width: 1)
    }
    
    /// 调试背景
    /// 
    /// - Parameter color: 背景颜色
    /// - Returns: 带调试背景的视图
    func debugBackground(_ color: Color = .yellow) -> some View {
        background(color.opacity(0.3))
    }
    
    /// 打印视图信息（仅在 DEBUG 模式下有效）
    ///
    /// - Parameter message: 消息
    /// - Returns: 原视图
    func debugPrint(_ message: String) -> some View {
        // 在 DEBUG 模式下可以启用日志输出
        // Log.debug("[Debug] \(message)")
        return self
    }
}
#endif

// MARK: - Toast扩展
// 注意：LocalToastManager相关功能已移至ToastManager.swift文件中
