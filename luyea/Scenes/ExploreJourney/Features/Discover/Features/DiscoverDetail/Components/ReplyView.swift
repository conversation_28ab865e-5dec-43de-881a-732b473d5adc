import SwiftUI

/// 回复视图组件
/// 
/// 用于显示单条回复的内容，包括用户头像、用户名和回复文本。
/// 这是一个可复用的组件，专门用于显示评论的回复。
struct ReplyView: View {
    
    // MARK: - Properties
    
    /// 回复数据
    let reply: DiscoverDetailModels.CommentReply
    
    // MARK: - Body
    
    var body: some View {
        HStack(alignment: .top, spacing: 10) {
            // 用户头像（比主评论稍小）
            userAvatar
            
            // 回复内容
            VStack(alignment: .leading, spacing: 4) {
                replyContent
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.leading, 8) // 回复相对主评论有缩进
        .padding(.vertical, 4)
    }
    
    // MARK: - Private Views
    
    /// 用户头像
    private var userAvatar: some View {
        AsyncImage(url: URL(string: reply.author.avatarUrl ?? "")) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Circle()
                .fill(Color(.systemGray5))
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                )
        }
        .frame(width: 24, height: 24) // 比主评论头像小
        .clipShape(Circle())
    }
    
    /// 回复内容视图
    private var replyContent: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 用户名和回复内容在同一行，支持多行显示
            replyTextContent
                .lineLimit(nil) // 允许多行显示
                .fixedSize(horizontal: false, vertical: true) // 确保垂直方向自适应

            // 时间、归属地和回复按钮
            replyMetaInfo
        }
    }

    /// 回复文本内容（包含@用户名）
    private var replyTextContent: some View {
        Group {
            if let replyToUser = reply.replyToUser {
                // 回复特定用户
                (Text(reply.author.username)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                + Text(" 回复 ")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.primary)
                + Text("@\(replyToUser.username)")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                + Text(": ")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.primary)
                + Text(reply.content)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.primary))
            } else {
                // 回复主评论
                (Text(reply.author.username)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                + Text(": ")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.primary)
                + Text(reply.content)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.primary))
            }
        }
    }
    
    /// 回复元信息（时间、归属地、回复按钮）
    private var replyMetaInfo: some View {
        HStack(spacing: 8) {
            // 发布时间
            Text(reply.createdAt.commentTimeDescription)
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(.secondary)
            
            // 归属地
            if let location = reply.author.location {
                Text(location)
                    .font(.system(size: 10, weight: .regular))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 回复按钮
            Button("回复") {
                // TODO: 实现回复功能
            }
            .font(.system(size: 10, weight: .regular))
            .foregroundColor(.secondary)
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 16) {
        ReplyView(
            reply: DiscoverDetailModels.CommentReply(
                id: "reply1",
                content: "说得很对，我也有同样的感受！",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user2",
                    username: "李四",
                    avatarUrl: nil,
                    isVerified: true,
                    location: "上海"
                ),
                createdAt: Date().addingTimeInterval(-300), // 5分钟前
                likeCount: 2,
                isLiked: false,
                replyToUser: nil, // 回复主评论
                parentReplyId: nil
            )
        )
        
        ReplyView(
            reply: DiscoverDetailModels.CommentReply(
                id: "reply2",
                content: "确实是这样，值得推荐给朋友们。",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user3",
                    username: "王五",
                    avatarUrl: nil,
                    isVerified: false,
                    location: "广州"
                ),
                createdAt: Date().addingTimeInterval(-3600), // 1小时前
                likeCount: 0,
                isLiked: false,
                replyToUser: DiscoverDetailModels.CommentAuthor(
                    id: "user2",
                    username: "李四",
                    avatarUrl: nil,
                    isVerified: true,
                    location: "上海"
                ), // 回复李四
                parentReplyId: "reply1"
            )
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
