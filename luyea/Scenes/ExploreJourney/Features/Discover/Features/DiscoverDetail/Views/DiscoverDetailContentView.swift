import SwiftUI

/// 发现详情页内容视图
///
/// 负责展示发现内容的详细信息，包括图片轮播、内容信息、
/// 相关旅程和评论等模块。
struct DiscoverDetailContentView: View {
    // MARK: - Properties
    let navigationParams: DiscoverDetailModels.NavigationParams
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel: DiscoverDetailViewModel

    /// 导航栏是否显示（用于控制返回按钮颜色）
    @State private var isNavigationBarVisible = false

    // MARK: - Initialization
    init(navigationParams: DiscoverDetailModels.NavigationParams) {
        self.navigationParams = navigationParams
        self._viewModel = StateObject(wrappedValue: DiscoverDetailViewModel(navigationParams: navigationParams))
    }

    // MARK: - Body

    var body: some View {
        mainContentScrollView
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle("")
            .navigationBarBackButtonHidden(true)
            .toolbarBackground(isNavigationBarVisible ? .visible : .hidden, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    customBackButton
                }
            }
            .hideTabBar()
            .overlay { fullScreenImageViewer }
            .sheet(isPresented: $viewModel.isAuthorProfilePresented) { authorProfileSheet }
            .sheet(isPresented: $viewModel.showJourneyDetail) { journeyDetailSheet }
            .onAppear { setupInitialState() }
    }

    // MARK: - Main Content Views

    // {{RIPER-5:
    //   Action: "Modified"
    //   Task_ID: "c714f906-2cb1-485e-b4d2-42f95c64ae0d"
    //   Timestamp: "2025-07-31T00:24:38+08:00"
    //   Authoring_Role: "LD"
    //   Principle_Applied: "SOLID-S (单一职责原则)"
    //   Quality_Check: "键盘避让功能已应用，确保评论输入时内容不被遮挡。"
    // }}
    private var mainContentScrollView: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 0) {
                imageCarouselSection
                    .background(
                        GeometryReader { geometry in
                            Color.clear
                                .onAppear {
                                    isNavigationBarVisible = false
                                }
                                .onChange(of: geometry.frame(in: .global).minY) { _, newValue in
                                    let shouldShowNavigationBar = newValue < -270
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        isNavigationBarVisible = shouldShowNavigationBar
                                    }
                                }
                        }
                    )

                VStack(alignment: .leading, spacing: 24) {
                    contentInfoSection
                    journeyRouteSection
                    commentsSection
                }
                .padding(.top, 16)
                .padding(.horizontal, 20)
            }
            .padding(.bottom, 40)
        }
        .ignoresSafeArea(edges: .top)
        .background(Color(.systemBackground))
    }


    // MARK: - Image Carousel Section

    /// 图片轮播区域，支持下拉拉伸效果
    private var imageCarouselSection: some View {
        GeometryReader { geometry in
            let offset = geometry.frame(in: .global).minY
            let baseHeight: CGFloat = 380

            // 计算拉伸高度和偏移量，实现下拉时的图片拉伸效果
            let stretchHeight = max(baseHeight, baseHeight + offset)
            let yOffset = offset > 0 ? -offset : 0

            let authorInfo = viewModel.authorInfo

            ImageCarouselSection(
                imageUrls: viewModel.imageUrls,
                currentPage: $viewModel.currentPage,
                isAutoScrolling: $viewModel.isAutoScrolling,
                authorAvatarUrl: authorInfo.avatarUrl,
                authorUsername: authorInfo.username,
                onImageTap: { viewModel.showFullScreenImageViewer() },
                onAuthorTap: { viewModel.showAuthorProfile() },
                onZoomChanged: { viewModel.handleZoomChanged($0) }
            )
            .frame(height: stretchHeight)
            .offset(y: yOffset)
            .clipped()
        }
        .frame(height: 380)
    }



    private var contentInfoSection: some View {
        let contentInfo = viewModel.contentInfo
        let stats = viewModel.stats
        return ContentInfoView(
            title: contentInfo.title,
            description: contentInfo.description,
            location: contentInfo.location ?? "",
            topic: viewModel.topic,
            isLiked: viewModel.isLiked,
            likeCount: stats.likeCount,
            onLike: { viewModel.handleLike() },
            onShare: { viewModel.handleShare() }
        )
    }

    @ViewBuilder
    private var journeyRouteSection: some View {
        if let journey = viewModel.journey {
            JourneyRouteCardView(
                journey: journey,
                onViewTap: { viewModel.showJourneyDetail = true },
                onForkTap: { print("复制行程功能待实现") }
            )
        }
    }

    // MARK: - Section Views

    private var commentsSection: some View {
        let stats = viewModel.stats
        return CommentsSection(
            comments: viewModel.comments,
            commentCount: stats.commentCount,
            commentText: $viewModel.commentText,
            onCommentSubmit: handleCommentSubmission,
            isCommentTextValid: isCommentTextValid,
            isLoadingComments: viewModel.isLoadingComments,
            hasMoreComments: viewModel.hasMoreComments,
            onLoadMoreComments: handleLoadMoreComments,
            loadingRepliesForComment: viewModel.loadingRepliesForComment,
            onLoadMoreReplies: handleLoadMoreReplies
        )
    }

    // MARK: - Navigation & Overlays

    /// 动态颜色的返回按钮
    private var customBackButton: some View {
        Button(action: { dismiss() }) {
            Image(systemName: "chevron.left")
                .font(.system(size: DesignSystemConstants.Navigation.backButtonIconSize,
                             weight: DesignSystemConstants.Navigation.backButtonFontWeight))
                .foregroundColor(isNavigationBarVisible ? .primary : .white)
                .padding(.leading, 8)
                .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }

    @ViewBuilder
    private var fullScreenImageViewer: some View {
        if viewModel.showFullScreenViewer {
            FullScreenImageViewerView(
                imageUrls: viewModel.imageUrls,
                currentPage: $viewModel.currentPage,
                isPresented: $viewModel.showFullScreenViewer,
                onZoomChanged: { viewModel.handleZoomChanged($0) }
            )
            .transition(.asymmetric(
                insertion: .scale(scale: 0.3, anchor: .center).combined(with: .opacity),
                removal: .scale(scale: 0.3, anchor: .center).combined(with: .opacity)
            ))
            .zIndex(1000)
        }
    }

    // MARK: - Sheet Views

    private var authorProfileSheet: some View {
        let authorInfo = viewModel.authorInfo
        return UserProfileView(
            username: authorInfo.username,
            avatarUrl: authorInfo.avatarUrl,
            isCurrentUser: false,
            contentTags: viewModel.topic.map { [$0.name] } ?? []
        )
    }

    @ViewBuilder
    private var journeyDetailSheet: some View {
        if let journey = viewModel.journey {
            JourneyDetailView(journey: journey)
        }
    }

    // MARK: - Helper Methods

    private func setupInitialState() {
        Task {
            await viewModel.loadDetailContent()
        }

        let imageCount = viewModel.imageUrls.count
        if viewModel.currentPage >= imageCount {
            viewModel.currentPage = 0
        }
    }

    private var isCommentTextValid: Bool {
        !viewModel.commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    private func handleCommentSubmission() {
        if isCommentTextValid {
            viewModel.handleCommentSubmission()
        }
    }

    /// 处理加载更多评论
    private func handleLoadMoreComments() {
        Task {
            await viewModel.loadComments(refresh: false)
        }
    }

    /// 处理加载更多回复
    private func handleLoadMoreReplies(for commentId: String) {
        Task {
            await viewModel.loadCommentReplies(for: commentId, refresh: false)
        }
    }

}

// MARK: - Preview

#Preview {
    DiscoverDetailContentView(
        navigationParams: DiscoverDetailModels.NavigationParams(
            id: "preview_001",
            previewData: DiscoverDetailModels.PreviewData(
                title: "美丽的自然风光探索之旅",
                coverImage: "https://c-ssl.dtstatic.com/uploads/blog/202408/14/9WSP7q6eh8wwMP6.thumb.1000_0.jpg",
                authorName: "旅行达人",
                authorAvatar: "https://img1.baidu.com/it/u=1747081318,2650263390&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"
            )
        )
    )
}
