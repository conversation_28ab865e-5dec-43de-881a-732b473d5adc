import SwiftUI

/// 旅程详情页视图
/// 展示完整的旅程路线信息
struct JourneyDetailView: View {
    
    // MARK: - Properties
    
    let journey: DiscoverDetailModels.JourneyRoute
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 头部信息
                    headerSection
                    
                    // 路线概览
                    overviewSection
                    
                    // 目的地列表
                    destinationsSection
                    
                    // 贴士和建议
                    tipsSection
                    
                    // 底部间距
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 16)
            }
            .navigationTitle("旅程详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 头部信息
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(journey.title)
                .font(.title2)
                .fontWeight(.bold)
            
            if let description = journey.description {
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            // 标签
            if !journey.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(journey.tags, id: \.self) { tag in
                            Text(tag)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(4)
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.horizontal, -16)
            }
        }
    }
    
    // MARK: - 路线概览
    
    private var overviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("路线概览")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                OverviewCard(
                    icon: "clock",
                    title: "行程时长",
                    value: "\(journey.duration)天"
                )
                
                OverviewCard(
                    icon: "location",
                    title: "目的地",
                    value: "\(journey.destinations.count)个"
                )
                
                OverviewCard(
                    icon: "dollarsign.circle",
                    title: "预估费用",
                    value: journey.estimatedCost.map { "¥\($0.min)-\($0.max)" } ?? "待定"
                )
                
                OverviewCard(
                    icon: "speedometer",
                    title: "难度等级",
                    value: difficultyText
                )
            }
        }
    }
    
    // MARK: - 目的地列表
    
    private var destinationsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("详细路线")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(journey.destinations.sorted(by: { $0.order < $1.order }), id: \.id) { destination in
                DestinationCard(destination: destination)
            }
        }
    }
    
    // MARK: - 贴士和建议
    
    private var tipsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("旅行贴士")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(journey.tips, id: \.self) { tip in
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "lightbulb")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .padding(.top, 2)
                        
                        Text(tip)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
    
    // MARK: - 计算属性
    
    private var difficultyText: String {
        return journey.difficulty.displayName
    }
}

// MARK: - 辅助组件

private struct OverviewCard: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .frame(maxWidth: .infinity)
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

private struct DestinationCard: View {
    let destination: DiscoverDetailModels.RouteDestination
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                // 序号
                Text("\(destination.order)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .frame(width: 20, height: 20)
                    .background(Color.blue)
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(destination.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(destination.location)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if let stayDuration = destination.stayDuration {
                    Text(stayDuration)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.green.opacity(0.1))
                        .foregroundColor(.green)
                        .cornerRadius(4)
                }
            }
            
            if let description = destination.description {
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - Preview

#Preview {
    JourneyDetailView(
        journey: DiscoverDetailModels.JourneyRoute(
            id: "journey_001",
            title: "无锡樱花季2日游",
            description: "专为樱花季设计的无锡深度游路线，涵盖鼋头渚、蠡园等赏樱胜地",
            duration: 2,
            destinations: [
                DiscoverDetailModels.RouteDestination(
                    id: "dest_001",
                    name: "鼋头渚风景区",
                    location: "无锡市滨湖区",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 31.5358, longitude: 120.2297),
                    order: 1,
                    stayDuration: "4小时",
                    description: "太湖第一名胜，樱花盛开时美不胜收",
                    imageUrl: "https://example.com/image.jpg",
                    attractions: [],
                    activities: ["赏樱", "摄影"],
                    tips: ["早上人少景美"]
                )
            ],
            totalDistance: 25.5,
            estimatedCost: DiscoverDetailModels.CostRange(min: 300, max: 500, currency: "CNY"),
            difficulty: .easy,
            forkCount: 127,
            tags: ["樱花", "春游", "摄影", "江南"],
            highlights: ["太湖第一名胜", "最美樱花季"],
            tips: ["3月中旬至4月上旬为最佳时间", "建议住宿太湖边酒店"],
            bestSeasons: ["春季"],
            createdAt: Date(),
            updatedAt: Date()
        )
    )
}
