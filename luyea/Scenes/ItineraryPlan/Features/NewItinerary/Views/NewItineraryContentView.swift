import Foundation
import SwiftUI

/// 新建行程内容视图
///
/// 新建行程模块的主要内容容器，负责显示底部弹窗、Tab导航和覆盖层。
/// 作为用户交互的核心界面，集成了行程创建的所有功能模块。
///
/// 主要功能：
/// - 底部弹窗容器和拖拽交互
/// - Tab导航和内容切换
/// - 头部导航和返回按钮
/// - Toast提示和加载状态
struct NewItineraryContentView: View {

    // MARK: - Properties

    @ObservedObject var contentManager: ContentManager
    let dismissAction: (() -> Void)?

    /// 头部导航覆盖
    private var headerOverlay: some View {
        VStack {
            // 返回按钮区域 - 在安全区域内，使用简单的顶部间距
            HStack {
                // 自定义返回按钮 (左上角)
                Button(action: {
                    dismissAction?()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: DesignSystemConstants.Navigation.backButtonIconSize,
                                     weight: DesignSystemConstants.Navigation.backButtonFontWeight))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(Color.black.opacity(0.3))
                        .clipShape(Circle())
                }

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)

            Spacer()
        }
    }



    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 主要内容容器
            if contentManager.isReady {
                contentContainer
                    .transition(.move(edge: .bottom).combined(with: .opacity))
            }

            // 集成的覆盖层功能
            // 覆盖层元素
            ZStack {
                // 头部导航 - 使用原有的悬浮圆形返回按钮设计
                if contentManager.isHeaderVisible {
                    headerOverlay
                        .transition(AnyTransition.move(edge: .top).combined(with: .opacity))
                }

                // 加载覆盖
                if contentManager.showLoadingOverlay {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()

                    VStack {
                        ProgressView()
                            .scaleEffect(1.2)

                        Text("加载中...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(.top, 8)
                    }
                    .transition(.opacity)
                }
            }
        }
        .onAppear {
            Log.debug("📱 内容层出现")
        }
        .onDisappear {
            Log.debug("📱 内容层消失")
        }
        // 错误提示弹窗
        .alert("错误", isPresented: $contentManager.showErrorAlert) {
            Button("确定") {
                contentManager.hideError()
            }
        } message: {
            if let error = contentManager.currentError {
                Text(error.localizedDescription)
            }
        }
    }
    
    // MARK: - Private Views
    
    /// 内容容器
    private var contentContainer: some View {
        VStack {
            Spacer()
            
            // 底部弹窗容器
            AdaptiveBottomSheetView(contentManager: contentManager)
                .ignoresSafeArea(.container, edges: .bottom)
        }
    }
}

/// 自适应底部弹窗视图
///
/// 简化的底部弹窗实现，直接使用ContentManager管理状态。
/// 移除冗余的状态管理器，简化架构设计。
struct AdaptiveBottomSheetView: View {

    // MARK: - Properties

    @ObservedObject var contentManager: ContentManager
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            sheetContent(geometry: geometry)
                .offset(y: contentManager.offsetY)
                .gesture(dragGesture)
                .onAppear {
                    contentManager.updateGeometry(geometry)
                }
        }
    }
    
    // MARK: - Private Views
    
    /// 弹窗内容
    @ViewBuilder
    private func sheetContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 8) {
            // 拖拽指示器
            dragHandle
            
            // Tab内容
            tabContent
                .padding(.top, 8)
        }
        .padding(.horizontal)
        .padding(.top, 8)
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        .background(sheetBackground)
        .topCornerRadius(DesignSystemConstants.CornerRadius.large)
        .compositingGroup()
    }
    
    /// 拖拽指示器
    private var dragHandle: some View {
        Capsule()
            .fill(Color.gray.opacity(0.3))
            .frame(width: 40, height: 5)
            .padding(.vertical, 8)
    }
    
    /// Tab内容
    private var tabContent: some View {
        ItineraryCreationView(contentManager: contentManager)
    }
    
    /// 弹窗背景
    private var sheetBackground: some View {
        Color(.systemGroupedBackground)
            .compositingGroup()
    }
    
    // MARK: - Gestures
    
    /// 拖拽手势
    private var dragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                if !contentManager.isDragging {
                    contentManager.handleDragStart()
                }
                contentManager.handleDragChange(value.translation.height)
            }
            .onEnded { value in
                let velocity = value.predictedEndTranslation.height - value.translation.height
                contentManager.handleDragEnd(velocity: velocity)
            }
    }
    
    // MARK: - Private Views
}

/// 行程创建主视图
///
/// 集成Tab导航和内容展示的核心视图，负责协调各个Tab页面的显示和交互。
struct ItineraryCreationView: View {

    // MARK: - Properties

    @ObservedObject var contentManager: ContentManager
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 16) {
            // Tab导航
            tabNavigation
            
            // Tab内容
            tabContentView
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        .compositingGroup()
    }
    
    /// Tab导航 - 使用增强的枚举架构
    private var tabNavigation: some View {
        HStack(spacing: 20) {
            ForEach(TabModel.allCases) { tab in
                customTabItem(for: tab)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }

    /// Tab项目实现 - 使用增强的枚举属性
    @ViewBuilder
    private func customTabItem(for tab: TabModel) -> some View {
        let isSelected = contentManager.currentTab == tab
        let iconName = isSelected ? tab.selectedIconName : tab.iconName
        let accentColor = tab.accentColor

        Button(action: { selectTab(tab) }) {
            VStack(spacing: 2) {
                // 标题视图（图标+文本）
                HStack(spacing: 6) {
                    Image(systemName: iconName)
                        .font(.system(size: 13, weight: isSelected ? .semibold : .regular))
                        .foregroundColor(isSelected ? accentColor : .secondary)

                    Text(tab.rawValue)
                        .font(isSelected ? .system(size: 15, weight: .bold) : .system(size: 15, weight: .regular))
                        .foregroundColor(isSelected ? accentColor : .secondary)
                }

                // 指示器 - 使用Tab的主题色
                if isSelected {
                    RoundedRectangle(cornerRadius: 1)
                        .fill(accentColor)
                        .frame(height: 2)
                } else {
                    RoundedRectangle(cornerRadius: 1)
                        .fill(.clear)
                        .frame(height: 2)
                }
            }
            .padding(.vertical, 4)
            .padding(.horizontal, 8)
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(.plain)
    }

    // MARK: - Private Methods

    /// 选择Tab
    private func selectTab(_ tab: TabModel) {
        guard tab != contentManager.currentTab else { return }

        withAnimation(.easeInOut(duration: 0.3)) {
            contentManager.currentTab = tab
        }

        handleTabSelectionChange()

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    /// 处理Tab选择变化
    private func handleTabSelectionChange() {
        // 当底部弹窗处于收缩状态时，切换Tab应该自动展开
        if contentManager.bottomSheetState == .collapsed {
            contentManager.updateBottomSheetState(.full, animated: true)
        }
    }
    
    /// Tab内容视图
    private var tabContentView: some View {
        TabView(selection: $contentManager.currentTab) {
            // 创建行程Tab
            CreateItineraryContentView(contentManager: contentManager)
                .tag(TabModel.create)

            // AI规划Tab
            AIPlanningContentView(contentManager: contentManager)
                .tag(TabModel.smart)

            // 导入攻略Tab
            ImportGuideContentView(contentManager: contentManager)
                .tag(TabModel.importGuide)
        }
        .tabViewStyle(.page(indexDisplayMode: .never))
    }
    
    // MARK: - Private Methods
    
    /// 处理Tab选择
    private func handleTabSelection() {
        // 只有当底部弹窗处于收缩状态时，切换tab才自动展开
        if contentManager.bottomSheetState == .collapsed {
            contentManager.updateBottomSheetState(.full, animated: true)
            Log.debug("📱 Tab被选择且弹窗处于收缩状态，自动展开弹窗")
        }
    }
}



// MARK: - Preview

#Preview("内容层") {
    ZStack {
        // 模拟地图背景
        LinearGradient(
            colors: [
                Color(.systemBlue).opacity(0.1),
                Color(.systemTeal).opacity(0.05)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        // 内容层
        NewItineraryContentView(
            contentManager: ContentManager(),
            dismissAction: nil
        )
    }
}


